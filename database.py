# -*- coding: utf-8 -*-
"""
نظام إدارة قاعدة البيانات للصيدلية
Pharmacy Database Management System
"""

import sqlite3
import os
from datetime import datetime

class PharmacyDatabase:
    def __init__(self, db_name="pharmacy.db"):
        """تهيئة قاعدة البيانات"""
        self.db_name = db_name
        self.init_database()
    
    def get_connection(self):
        """إنشاء اتصال بقاعدة البيانات"""
        conn = sqlite3.connect(self.db_name)
        conn.row_factory = sqlite3.Row  # للحصول على النتائج كقاموس
        return conn
    
    def init_database(self):
        """إنشاء الجداول الأساسية"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # جدول الأدوية
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS medicines (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                scientific_name TEXT,
                company TEXT,
                category TEXT,
                price REAL NOT NULL,
                cost_price REAL,
                quantity INTEGER DEFAULT 0,
                min_quantity INTEGER DEFAULT 10,
                expiry_date TEXT,
                barcode TEXT UNIQUE,
                description TEXT,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                updated_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول المبيعات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS sales (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                sale_date TEXT NOT NULL,
                total_amount REAL NOT NULL,
                discount REAL DEFAULT 0,
                final_amount REAL NOT NULL,
                payment_method TEXT DEFAULT 'نقدي',
                customer_name TEXT,
                customer_phone TEXT,
                notes TEXT,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول تفاصيل المبيعات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS sale_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                sale_id INTEGER NOT NULL,
                medicine_id INTEGER NOT NULL,
                medicine_name TEXT NOT NULL,
                quantity INTEGER NOT NULL,
                unit_price REAL NOT NULL,
                total_price REAL NOT NULL,
                FOREIGN KEY (sale_id) REFERENCES sales (id),
                FOREIGN KEY (medicine_id) REFERENCES medicines (id)
            )
        ''')
        
        # جدول المشتريات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS purchases (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                medicine_id INTEGER NOT NULL,
                quantity INTEGER NOT NULL,
                cost_price REAL NOT NULL,
                total_cost REAL NOT NULL,
                supplier TEXT,
                purchase_date TEXT NOT NULL,
                expiry_date TEXT,
                notes TEXT,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (medicine_id) REFERENCES medicines (id)
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def add_medicine(self, medicine_data):
        """إضافة دواء جديد"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute('''
                INSERT INTO medicines (name, scientific_name, company, category, 
                                     price, cost_price, quantity, min_quantity, 
                                     expiry_date, barcode, description)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                medicine_data['name'],
                medicine_data.get('scientific_name', ''),
                medicine_data.get('company', ''),
                medicine_data.get('category', ''),
                medicine_data['price'],
                medicine_data.get('cost_price', 0),
                medicine_data.get('quantity', 0),
                medicine_data.get('min_quantity', 10),
                medicine_data.get('expiry_date', ''),
                medicine_data.get('barcode', ''),
                medicine_data.get('description', '')
            ))
            
            medicine_id = cursor.lastrowid
            conn.commit()
            return medicine_id
            
        except sqlite3.IntegrityError as e:
            if "barcode" in str(e):
                raise ValueError("الباركود موجود مسبقاً")
            else:
                raise ValueError("خطأ في إضافة الدواء")
        finally:
            conn.close()
    
    def get_all_medicines(self):
        """الحصول على جميع الأدوية"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT * FROM medicines 
            ORDER BY name
        ''')
        
        medicines = cursor.fetchall()
        conn.close()
        return medicines
    
    def search_medicines(self, search_term):
        """البحث عن الأدوية"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT * FROM medicines 
            WHERE name LIKE ? OR scientific_name LIKE ? OR barcode LIKE ?
            ORDER BY name
        ''', (f'%{search_term}%', f'%{search_term}%', f'%{search_term}%'))
        
        medicines = cursor.fetchall()
        conn.close()
        return medicines
    
    def update_medicine(self, medicine_id, medicine_data):
        """تحديث بيانات الدواء"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute('''
                UPDATE medicines 
                SET name=?, scientific_name=?, company=?, category=?, 
                    price=?, cost_price=?, quantity=?, min_quantity=?, 
                    expiry_date=?, barcode=?, description=?, 
                    updated_at=CURRENT_TIMESTAMP
                WHERE id=?
            ''', (
                medicine_data['name'],
                medicine_data.get('scientific_name', ''),
                medicine_data.get('company', ''),
                medicine_data.get('category', ''),
                medicine_data['price'],
                medicine_data.get('cost_price', 0),
                medicine_data.get('quantity', 0),
                medicine_data.get('min_quantity', 10),
                medicine_data.get('expiry_date', ''),
                medicine_data.get('barcode', ''),
                medicine_data.get('description', ''),
                medicine_id
            ))
            
            conn.commit()
            return cursor.rowcount > 0
            
        except sqlite3.IntegrityError:
            raise ValueError("الباركود موجود مسبقاً")
        finally:
            conn.close()
    
    def delete_medicine(self, medicine_id):
        """حذف دواء"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('DELETE FROM medicines WHERE id=?', (medicine_id,))
        
        success = cursor.rowcount > 0
        conn.commit()
        conn.close()
        return success
    
    def get_low_stock_medicines(self):
        """الحصول على الأدوية منخفضة المخزون"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT * FROM medicines 
            WHERE quantity <= min_quantity
            ORDER BY quantity ASC
        ''')
        
        medicines = cursor.fetchall()
        conn.close()
        return medicines
